<script setup lang="ts">
import type { RecordItem } from '@/api/monitor.ts'
import dayjs from 'dayjs'
import { MonitorAPI } from '@/api/monitor.ts'
import { formatDuration } from '@/utils'

const { deviceId, channelId } = defineProps<{
  deviceId: string
  channelId: string
}>()

const tableList = ref<RecordItem[]>([])
const timeRange = ref([dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')])
const loading = ref(false)
function handleQuery() {
  loading.value = true
  const [startTime, endTime] = timeRange.value
  MonitorAPI.deviceRecord(deviceId, channelId, startTime, endTime).then((res) => {
    tableList.value = res.recordList || []
  }).finally(() => {
    loading.value = false
  })
}

handleQuery()

/**
 * 播放录像
 */
const current = ref<RecordItem>(null)
const monitorDialog = ref(false)
const formData = ref<RecordItem>()
function play(row: RecordItem) {
  current.value = row
  formData.value = { ...row }
  monitorDialog.value = true
}

function dialogClose() {
  current.value = null
}
</script>

<template>
  <div v-loading="loading" class="flex flex-col">
    <header class="flex items-center gap-5 h-22 mb-10">
      <i class="i-temp-monitor" text="16 primary" />
      <span>录像片段</span>
      <span>（{{ tableList.length }}个）</span>
    </header>
    <div class="flex justify-end mb-10">
      <el-date-picker
        v-model="timeRange"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD HH:mm:ss"
        @change="handleQuery"
      />
    </div>
    <el-scrollbar height="calc(100vh - 18.5vw)">
      <div v-if="tableList.length" class="flex flex-col gap-10 leading-base">
        <div v-for="item in tableList" :key="item.filePath" :class="{ active: current === item }" class="rounded-4 cursor-pointer" p="x-16 y-12" b="~ border" @click="play(item)">
          <div class="flex justify-between items-center">
            <span>{{ dayjs(item.startTime).format('HH:mm:ss') }} - {{ dayjs(item.endTime).format('HH:mm:ss') }}</span>
            <el-tag type="info">{{ formatDuration(dayjs(item.endTime).diff(dayjs(item.startTime))) }}</el-tag>
          </div>
          <div class="mt-6" text="text-secondary">{{ item.name }}</div>
        </div>
      </div>
      <el-empty v-else />
    </el-scrollbar>
    <PlayRecordDialog v-if="monitorDialog" v-model="monitorDialog" :device-id="deviceId" :channel-id="channelId" :start-time="formData?.startTime" :end-time="formData?.endTime" @close="dialogClose" />
  </div>
</template>

<style scoped lang="scss">
.active {
  background-color: var(--el-color-primary-9);
  border-color: var(--el-color-primary);
}
</style>
