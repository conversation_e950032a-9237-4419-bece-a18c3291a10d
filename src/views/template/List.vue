<script setup lang="ts">
import { ArrowDown, ChatDotRound, CollectionTag, Search, Star } from '@element-plus/icons-vue'

const activeType = ref('1')
const typeList = [
  { label: '文章', value: '1' },
  { label: '应用', value: '2' },
  { label: '项目', value: '3' },
  { label: '图片', value: '4' },
  { label: '新闻', value: '5' },
]

function changeType(value: string) {
  activeType.value = value
}

const activeCategory = ref('all')
const categoryList = [
  { label: '全部', value: 'all' },
  { label: '类目一', value: '1' },
  { label: '类目二', value: '2' },
  { label: '类目三', value: '3' },
  { label: '类目四', value: '4' },
  { label: '类目五', value: '5' },
  { label: '类目六', value: '6' },
  { label: '类目七', value: '7' },
  { label: '类目八', value: '8' },
  { label: '类目九', value: '9' },
  { label: '类目十', value: '10' },
  { label: '类目十一', value: '11' },
  { label: '类目十二', value: '12' },
  { label: '类目十三', value: '13' },
  { label: '类目十四', value: '14' },
  { label: '类目十五', value: '15' },
  { label: '类目十六', value: '16' },
  { label: '类目十七', value: '17' },
  { label: '类目十八', value: '18' },
  { label: '类目十九', value: '19' },
  { label: '类目二十', value: '20' },
]

function changeCategory(value: string) {
  activeCategory.value = value
}

const value1 = ref([])

const expanded = ref(false)
function toggleExpanded() {
  expanded.value = !expanded.value
}
</script>

<template>
  <div class="flex flex-col gap-$gap">
    <section class="bg-bg rounded-4" b="~ border-lighter">
      <header class="flex items-center leading-extra-large" p="x-24 y-16" b="b border" text="20">
        <span class="font-bold">搜索列表</span>
        <div class="w-1 h-12 bg-border-lighter mx-12" />
        <span text="14 text-secondary">这是描述文字</span>
      </header>
      <div class="flex justify-between" p="x-24 y-16">
        <aside class="flex gap-32">
          <template v-for="item in typeList" :key="item.value">
            <el-button v-if="activeType === item.value" type="primary" class="!w-80" @click="changeType(item.value)">{{ item.label }}</el-button>
            <el-button v-else text class="!w-80" @click="changeType(item.value)">{{ item.label }}</el-button>
          </template>
        </aside>
        <aside>
          <el-input :prefix-icon="Search" class="!w-400" placeholder="请输入关键词" />
        </aside>
      </div>
    </section>

    <section class="bg-bg rounded-4 px-32" b="~ border-lighter">
      <header class="flex justify-between py-16" b="b border">
        <aside class="flex">
          <div class="w-100 h-22 shrink-0 leading-22">所属类目：</div>
          <div class="hTransition flex gap-24 flex-wrap overflow-hidden" :class="expanded ? 'h-auto' : 'h-24'">
            <template v-for="item in categoryList" :key="item.value">
              <el-button v-if="activeCategory === item.value" type="primary" class="!ml-0 text-14" size="small" @click="changeCategory(item.value)">{{ item.label }}</el-button>
              <el-button v-else size="small" text class="!ml-0 text-14" @click="changeCategory(item.value)">{{ item.label }}</el-button>
            </template>
          </div>
        </aside>
        <aside>
          <el-button size="small" type="primary" text @click="toggleExpanded">
            <span class="text-14 mr-8">{{ expanded ? '收起' : '展开' }}</span>
            <el-icon><ArrowDown /></el-icon>
          </el-button>
        </aside>
      </header>

      <div class="flex items-center py-16" b="b border">
        <div class="w-100 h-32 leading-32 shrink-0">所有者：</div>
        <el-select v-model="value1" class="!w-280" multiple>
          <el-option label="奥特曼" :value="1" />
          <el-option label="马斯克" :value="2" />
        </el-select>
        <el-button size="small" text class="!ml-0" type="primary">只看自己的</el-button>
      </div>

      <div class="flex py-16">
        <div class="w-100 h-32 leading-32 shrink-0">其它选项：</div>
        <el-form class="flex gap-x-48 gap-y-16 flex-wrap">
          <el-form-item label="活跃用户：">
            <el-select v-model="value1" class="!w-200" multiple>
              <el-option label="奥特曼" :value="1" />
              <el-option label="马斯克" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="好评度：">
            <el-select v-model="value1" class="!w-200" multiple>
              <el-option label="奥特曼" :value="1" />
              <el-option label="马斯克" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="所属类别：">
            <el-select v-model="value1" class="!w-200" multiple>
              <el-option label="奥特曼" :value="1" />
              <el-option label="马斯克" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="所属类别：">
            <el-select v-model="value1" class="!w-200" multiple>
              <el-option label="奥特曼" :value="1" />
              <el-option label="马斯克" :value="2" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </section>

    <section class="bg-bg rounded-4 px-32 py-16" b="~ border-lighter">
      <div v-for="item in 4" :key="item">
        <header class="text-medium font-bold leading-medium mb-12">这里是一个标题</header>
        <div class="flex gap-10">
          <el-tag effect="plain" type="info">设计文档</el-tag>
          <el-tag effect="plain" type="info">设计语言</el-tag>
          <el-tag effect="plain" type="info">交互方式</el-tag>
        </div>
        <p class="my-16 leading-base" text="text-regular">段落示意：设计平台 design。alipay。com，用最小的工作量，无缝接入蚂蚁金服生态，提供跨越设计与开发的体验解决方案。设计平台 design。alipay。com，用最小的工作量，无缝接入蚂蚁金服生态，提供跨越设计与开发的体验解决方案。段落示意：设计平台 design。alipay。com，用最小的工作量，无缝接入蚂蚁金服生态，提供跨越设计与开发的体验解决方案。设计平台 design。alipay。com，用最小的工作量，无缝接入生态，提供跨越设计与开发的体验解决方案。</p>
        <div class="flex items-center">
          <img src="@/assets/temp/avatar.webp" class="size-24" />
          <span class="ml-8" text="primary">清舟逐月</span>
          <span class="mx-4" text="text-secondary">发布在</span>
          <span text="#1890FF">design.alipay.com</span>
          <span class="ml-18" text="text-placeholder">2025-10-01 18:00</span>
        </div>
        <footer class="mt-16 h-32 flex items-center" text="text-secondary">
          <el-icon text="16"><Star /></el-icon>
          <span class="ml-8">15</span>
          <div class="w-1 h-16 bg-border-lighter mx-32" />
          <el-icon text="16"><ChatDotRound /></el-icon>
          <span class="ml-8">23</span>
          <div class="w-1 h-16 bg-border-lighter mx-32" />
          <el-icon text="16"><CollectionTag /></el-icon>
          <span class="ml-8">18</span>
        </footer>
        <div class="my-12 h-1 bg-border-lighter" />
      </div>
      <footer class="flex justify-center">
        <el-button class="!w-136">加载更多</el-button>
      </footer>
    </section>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-form-item) {
  margin-bottom: 0;
}

.hTransition {
  transition: height 0.3s;
}
</style>
