<!-- 表格基本模板，增删改查 -->
<script setup lang="ts">
import type { User } from '@/api/system/user.ts'
import { Plus, Refresh } from '@element-plus/icons-vue'
import { UserAPI } from '@/api/system/user.ts'
import { getState } from '@/hooks'

const route = useRoute()
const state = getState<User>()
const { queryParams, tableList, loading, dialogVisible, formData, title, total, rules, submitting } = toRefs(state)
rules.value = {
  username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  nickname: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  roleName: [{ required: true, message: '请选择角色', trigger: 'change' }],
}

function handleQuery() {
  loading.value = true
  UserAPI.pageList(queryParams.value).then((res) => {
    tableList.value = res.records
    total.value = res.total
  }).finally(() => {
    loading.value = false
  })
}

handleQuery()

function search() {
  queryParams.value.pageNum = 1
  handleQuery()
}

const searchFormRef = useTemplateRef('searchFormRef')
function refreshSearch() {
  searchFormRef.value.resetFields()
  search()
}

function add() {
  formData.value = { enabled: true }
  title.value = '新增'
  dialogVisible.value = true
}

function edit(row: typeof state.formData) {
  formData.value = { ...row }
  title.value = '编辑'
  dialogVisible.value = true
}

const formRef = useTemplateRef('formRef')
function submit() {
  formRef.value.validate().then(() => {
    submitting.value = true
    const userId = formData.value.id
    const fn = userId ? UserAPI.update : UserAPI.add
    fn(formData.value).then(() => {
      ElMessage.success('操作成功！')
      dialogVisible.value = false
      handleQuery()
    }).finally(() => {
      submitting.value = false
    })
  })
}

function handleDelete(userId: number) {
  ElMessageBox.confirm('确认删除已选中的数据项?', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    loading.value = true
    UserAPI.delete(userId)
      .then(() => {
        ElMessage.success('操作成功！')
        if (tableList.value.length === 1 && queryParams.value.pageNum > 1) {
          queryParams.value.pageNum -= 1
        }

        handleQuery()
      })
      .finally(() => {
        loading.value = false
      })
  },
  )
}
</script>

<template>
  <div class="table-page">
    <header class="page-header">
      <header class="title">
        <span class="font-bold">搜索列表</span>
        <div class="w-1 h-12 bg-border-lighter mx-12" />
        <span text="14 text-secondary">这是描述文字</span>
      </header>
      <div class="form">
        <el-form ref="searchFormRef" inline :model="queryParams">
          <el-form-item label="姓名" prop="nickName">
            <el-input v-model="queryParams.nickName" placeholder="请输入" @change="search" />
          </el-form-item>
          <el-form-item label="账号" prop="username">
            <el-input v-model="queryParams.username" placeholder="请输入" @change="search" />
          </el-form-item>
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="queryParams.phone" placeholder="请输入" @change="search" />
          </el-form-item>
          <el-form-item label="角色" prop="roleName">
            <el-input v-model="queryParams.roleName" placeholder="请输入" @change="search" />
          </el-form-item>
        </el-form>
        <el-button :icon="Refresh" @click="refreshSearch">重置</el-button>
      </div>
    </header>

    <main v-loading="loading" class="page-main">
      <header class="header">
        <span class="font-bold" text="18">{{ route.meta.title }}</span>
        <el-button class="ml-auto" type="primary" :icon="Plus" @click="add">新增</el-button>
        <div class="w-1 h-16 mx-18 bg-$el-border-color" />
        <i class="i-base-fullscreen cursor-pointer transition-all" text="16" hover="scale-120" />
        <i class="i-base-refresh ml-10 cursor-pointer transition-all" text="16" hover="scale-120" />
      </header>
      <el-table
        stripe
        :data="tableList"
        highlight-current-row
      >
        <el-table-column prop="nickname" label="姓名" min-width="120" show-overflow-tooltip />
        <el-table-column prop="username" label="账号" min-width="120" show-overflow-tooltip />
        <el-table-column prop="phone" label="手机号" min-width="140" show-overflow-tooltip />
        <el-table-column prop="roleNames" label="角色" min-width="120" show-overflow-tooltip />
        <el-table-column prop="modifyBy" label="更新人" min-width="120" show-overflow-tooltip />
        <el-table-column prop="modifyTime" label="更新时间" min-width="160" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" min-width="160">
          <template #default="scope">
            <el-link type="primary" underline="never" @click="edit(scope.row)">编辑</el-link>
            <el-link class="mx-$Space-Default-12" type="danger" underline="never" @click="handleDelete(scope.row.id)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-if="tableList.length"
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="mt-20 ml-auto"
        @size-change="handleQuery"
        @current-change="handleQuery"
      />
    </main>

    <el-dialog v-model="dialogVisible" destroy-on-close width="40vw" :title="title" append-to-body :close-on-click-modal="false">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="4.3vw" class="p-30" size="large">
        <el-form-item label="账号" prop="username">
          <el-input v-model="formData.username" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="姓名" prop="nickName">
          <el-input v-model="formData.nickname" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input v-model="formData.phone" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="角色" prop="roleName">
          <el-input v-model="formData.roleName" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="状态" prop="enabled">
          <el-radio-group v-model="formData.enabled">
            <el-radio :value="true">启用</el-radio>
            <el-radio :value="false">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button :loading="submitting" type="primary" @click="submit">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped></style>
