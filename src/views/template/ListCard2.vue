<script setup lang="ts">
import { ArrowDown, Search } from '@element-plus/icons-vue'
import avatar1 from '@/assets/temp/avatar1.png'
import avatar2 from '@/assets/temp/avatar2.png'
import avatar3 from '@/assets/temp/avatar3.png'
import avatar4 from '@/assets/temp/avatar4.png'
import avatar from '@/assets/temp/avatar.webp'

const activeType = ref('1')
const typeList = [
  { label: '文章', value: '1' },
  { label: '应用', value: '2' },
  { label: '项目', value: '3' },
  { label: '图片', value: '4' },
  { label: '新闻', value: '5' },
]

function changeType(value: string) {
  activeType.value = value
}

const activeCategory = ref('all')
const categoryList = [
  { label: '全部', value: 'all' },
  { label: '类目一', value: '1' },
  { label: '类目二', value: '2' },
  { label: '类目三', value: '3' },
  { label: '类目四', value: '4' },
  { label: '类目五', value: '5' },
  { label: '类目六', value: '6' },
  { label: '类目七', value: '7' },
  { label: '类目八', value: '8' },
  { label: '类目九', value: '9' },
  { label: '类目十', value: '10' },
  { label: '类目十一', value: '11' },
  { label: '类目十二', value: '12' },
  { label: '类目十三', value: '13' },
  { label: '类目十四', value: '14' },
  { label: '类目十五', value: '15' },
  { label: '类目十六', value: '16' },
  { label: '类目十七', value: '17' },
  { label: '类目十八', value: '18' },
  { label: '类目十九', value: '19' },
  { label: '类目二十', value: '20' },
]

function changeCategory(value: string) {
  activeCategory.value = value
}

const value1 = ref([])

const expanded = ref(false)
function toggleExpanded() {
  expanded.value = !expanded.value
}

const personList = ref([
  { id: 1, avatar, name: '奥特曼' },
  { id: 2, avatar: avatar1, name: '马斯克' },
  { id: 3, avatar: avatar2, name: '钢铁侠' },
  { id: 4, avatar: avatar3, name: '蝙蝠侠' },
  { id: 5, avatar: avatar4, name: '绿巨人' },
  { id: 6, avatar: avatar4, name: '美国队长' },
  { id: 7, avatar, name: '蜘蛛侠' },
  { id: 8, avatar, name: '黑寡妇' },
  { id: 9, avatar, name: '奇异博士' },
  { id: 10, avatar, name: '灭霸' },
])
</script>

<template>
  <div class="flex flex-col gap-$gap">
    <section class="bg-bg rounded-4" b="~ border-lighter">
      <header class="flex items-center leading-extra-large" p="x-24 y-16" b="b border" text="20 textPrimary">
        <span class="font-bold">搜索列表</span>
        <div class="w-1 h-12 bg-border-lighter mx-12" />
        <span text="14 text-secondary">这是描述文字</span>
      </header>
      <div class="flex justify-between" p="x-24 y-16">
        <aside class="flex gap-32">
          <template v-for="item in typeList" :key="item.value">
            <el-button v-if="activeType === item.value" type="primary" class="!w-80" @click="changeType(item.value)">{{ item.label }}</el-button>
            <el-button v-else text class="!w-80" @click="changeType(item.value)">{{ item.label }}</el-button>
          </template>
        </aside>
        <aside>
          <el-input :prefix-icon="Search" class="!w-400" placeholder="请输入关键词" />
        </aside>
      </div>
    </section>

    <section class="bg-bg rounded-4 px-32" b="~ border-lighter">
      <header class="flex justify-between py-16" b="b border">
        <aside class="flex">
          <div class="w-100 h-22 shrink-0 leading-22">所属类目：</div>
          <div class="hTransition flex gap-24 flex-wrap overflow-hidden" :class="expanded ? 'h-auto' : 'h-24'">
            <template v-for="item in categoryList" :key="item.value">
              <el-button v-if="activeCategory === item.value" type="primary" class="!ml-0 text-14" size="small" @click="changeCategory(item.value)">{{ item.label }}</el-button>
              <el-button v-else size="small" text class="!ml-0 text-14" @click="changeCategory(item.value)">{{ item.label }}</el-button>
            </template>
          </div>
        </aside>
        <aside>
          <el-button size="small" type="primary" text @click="toggleExpanded">
            <span class="text-14 mr-8">{{ expanded ? '收起' : '展开' }}</span>
            <el-icon><ArrowDown /></el-icon>
          </el-button>
        </aside>
      </header>

      <div class="flex items-center py-16" b="b border">
        <div class="w-100 h-32 leading-32 shrink-0">所有者：</div>
        <el-select v-model="value1" class="!w-280" multiple>
          <el-option label="奥特曼" :value="1" />
          <el-option label="马斯克" :value="2" />
        </el-select>
        <el-button size="small" text class="!ml-0" type="primary">只看自己的</el-button>
      </div>

      <div class="flex py-16">
        <div class="w-100 h-32 leading-32 shrink-0">其它选项：</div>
        <el-form class="flex gap-x-48 gap-y-16 flex-wrap">
          <el-form-item label="活跃用户：">
            <el-select v-model="value1" class="!w-200" multiple>
              <el-option label="奥特曼" :value="1" />
              <el-option label="马斯克" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="好评度：">
            <el-select v-model="value1" class="!w-200" multiple>
              <el-option label="奥特曼" :value="1" />
              <el-option label="马斯克" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="所属类别：">
            <el-select v-model="value1" class="!w-200" multiple>
              <el-option label="奥特曼" :value="1" />
              <el-option label="马斯克" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="所属类别：">
            <el-select v-model="value1" class="!w-200" multiple>
              <el-option label="奥特曼" :value="1" />
              <el-option label="马斯克" :value="2" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </section>

    <section class="bg-bg rounded-4 px-32 py-16 gap-24" b="~ border-lighter" grid="~ cols-5">
      <div v-for="item in 70" :key="item" class="p-24" b="~ border">
        <div class="size-32">
          <i class="i-temp-github text-red" />
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-form-item) {
  margin-bottom: 0;
}

.hTransition {
  transition: height 0.3s;
}
</style>
