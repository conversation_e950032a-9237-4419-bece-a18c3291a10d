import type { App } from 'vue'
import { defineStore } from 'pinia'
import { storageKey } from '@/constants'
import { store } from '@/store/index'

export const useAppStore = defineStore('app', {
  state: () => ({
    // 当前页面尺寸相对于1920的缩放比例
    ratio: getRatio(),
    isCollapse: false,
    theme: localStorage.getItem(storageKey.theme) || 'light' as 'light' | 'dark',
  }),
  actions: {},
})

/**
 * 用于在组件外部（如在Pinia Store 中）使用 Pinia 提供的 store 实例。
 * 官方文档解释了如何在组件外部使用 Pinia Store：
 * https://pinia.vuejs.org/core-concepts/outside-component-usage.html#using-a-store-outside-of-a-component
 */
export function useAppStoreHook() {
  return useAppStore(store)
}

/**
 * 获取相对于1920的缩放比例
 */
export function getRatio(): number {
  const width = document.documentElement.clientWidth
  return width / 1920
}

/**
 * 将ratio注入全局变量
 */
export function injectRatio(app: App) {
  const appStore = useAppStoreHook()
  // const rationRef = ref(appStore.ratio)
  // app.config.globalProperties.$ratio = rationRef
  // watch(() => appStore.ratio, (newVal) => {
  //   rationRef.value = newVal
  // })

  Object.defineProperties(app.config.globalProperties, {
    $ratio: {
      enumerable: true,
      configurable: true,
      get: () => appStore.ratio,
    },
  })
}
