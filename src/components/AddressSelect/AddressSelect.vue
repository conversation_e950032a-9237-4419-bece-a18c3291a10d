<script setup lang="ts">
import type { AddressInfo } from '#/index'
import type { <PERSON><PERSON>, TianPois } from '#/map'
import { Loading, LocationInformation, Search } from '@element-plus/icons-vue'
import L from 'leaflet'
import imgActive from '@/assets/images/marker-active.svg'
import img from '@/assets/images/marker.svg'
import { useOnceFn } from '@/hooks/useOnceFn.ts'
import { latLngToAddress, searchAddress } from '@/utils'

const { editable = true } = defineProps<{
  /**
   * 编辑模式还是详情模式
   */
  editable?: boolean
}>()

/**
 * 提交时发射事件
 */
const emit = defineEmits<{
  (e: 'change', data: { latitude: number, longitude: number, address: string }): void
}>()

const dialogVisible = defineModel<boolean>()

/**
 * 组件外部传进来的经纬度数据
 */
const data = defineModel<AddressInfo>('data')

const DEFAULT_DATA = { latitude: 30.505399066242695, longitude: 103.62545013427736, address: '四川省成都市大邑县安仁古镇' }

/**
 * 打开弹窗后初始化地图
 */
watch(dialogVisible, (val) => {
  if (val) {
    latlng.lat = data.value.latitude || DEFAULT_DATA.latitude
    latlng.lng = data.value.longitude || DEFAULT_DATA.longitude
    address.value = data.value.address || DEFAULT_DATA.address
    poiList.value = []

    nextTick(() => {
      // 只在第一次打开弹窗时初始化地图
      onceInit()
      marker.addTo(map)
      marker.setLatLng(latlng)
      map.flyTo(latlng, 13, { duration: 1.5 })
    })
  }
})

const mapRef = useTemplateRef('mapRef')
let map: L.Map

const latlng = reactive({ lat: null, lng: null })
const icon = L.icon({
  iconUrl: img,
  iconSize: [33, 43], // 图标大小
  iconAnchor: [17, 41], // 图标锚点
})

const iconActive = L.icon({
  iconUrl: imgActive,
  iconSize: [49.5, 64.5], // 图标大小
  iconAnchor: [25, 61], // 图标锚点
})
const marker = L.marker(latlng, {
  icon: iconActive,
})
const onceInit = useOnceFn(() => {
  // 使用 id 为 map 的 div 容器初始化地图，同时指定地图的中心点和缩放级别
  map = L.map(mapRef.value, {
    center: [31.626748, 105.817774],
    zoom: 18,
    attributionControl: false,
    zoomControl: false,
  })
  // 加载高德地图
  L.tileLayer('//webrd02.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', {
    tileSize: 256,
    maxZoom: 20,
    maxNativeZoom: 18,
    minZoom: 6,
    bounds: [
      [50.36729928578421, 68.55468750000001],
      [-3.6286541049128727, 141.32812500000003],
    ],
  }).addTo(map)
  marker.addTo(map)
  map.on('click', onClick)
})

/**
 * 注册点击事件
 */
const address = ref('')
const loading = ref(false)
let timer
function onClick(e: L.LeafletMouseEvent) {
  if (!editable)
    return
  marker.addTo(map)
  poiList.value = []
  clearTimeout(timer)
  // 小于500ms就不显示加载状态了
  timer = setTimeout(() => {
    loading.value = true
  }, 500)
  latlng.lat = e.latlng.lat
  latlng.lng = e.latlng.lng
  marker.setLatLng(e.latlng)
  // 经纬度 =》 地址信息
  latLngToAddress(e.latlng.lat, e.latlng.lng)
    .then((locationInfo) => {
      address.value = locationInfo
    })
    .catch(error => console.error(error))
    .finally(() => {
      clearTimeout(timer)
      loading.value = false
    })
}

const provinceCity = reactive({
  province: null,
  city: null,
})

/**
 * 地址 =》 经纬度
 */
const poiList = ref<TianPois[]>([])
const currentPos = ref<TianPois>()
function search() {
  if (!address.value)
    return
  // 小于500ms就不显示加载状态了
  clearTimeout(timer)
  timer = setTimeout(() => {
    loading.value = true
  }, 500)
  poiList.value = []
  const { province, city } = provinceCity
  searchAddress(address.value, province, city).then((res) => {
    poiList.value = res
  }).finally(() => {
    clearTimeout(timer)
    loading.value = false
  })
}

/**
 * 将所有点位添加到地图上
 */
const markerList = ref<L.Marker[]>([])
watch(poiList, (newVal) => {
  for (const item of markerList.value) {
    item.remove()
  }

  markerList.value = []
  for (const item of newVal) {
    const { lng, lat } = item
    const tempMarker = L.marker({ lat, lng }, { icon })
    tempMarker.addTo(map)
    tempMarker.on('click', () => {
      select(item)
    })
    markerList.value.push(tempMarker)
  }

  if (markerList.value.length) {
    select(poiList.value[0])
  }
})

/**
 * 点击列表选中点位
 */
let preMarker: L.Marker
function select(row: TianPois) {
  marker.remove()
  if (preMarker) {
    preMarker.setIcon(icon)
  }

  const tempMarker = markerList.value.find((item) => {
    const { lat, lng } = item.getLatLng()
    return lat === row.lat && lng === row.lng
  })
  if (tempMarker) {
    tempMarker.setIcon(iconActive)
    preMarker = tempMarker
  }

  currentPos.value = row
  address.value = row.fullAddress
  latlng.lat = row.lat
  latlng.lng = row.lng
  map.setView(tempMarker.getLatLng(), 18)
}

function submit() {
  data.value.address = address.value
  data.value.latitude = latlng.lat
  data.value.longitude = latlng.lng
  emit('change', { latitude: latlng.lat, longitude: latlng.lng, address: address.value })
  dialogVisible.value = false
}
</script>

<template>
  <el-dialog v-model="dialogVisible" width="55.6vw" align-center :title="editable ? '地址选择' : '地址详情' " append-to-body :close-on-click-modal="false">
    <div class="relative w-full h-600">
      <div ref="mapRef" class="w-full h-full" />
      <div class="absolute z-9999 left-16 top-16">
        <aside class="w-240 h-full flex flex-col">
          <ProvinceCitySelect v-if="editable" v-model="provinceCity" @change="search" />
          <el-input v-if="editable" v-model="address" :suffix-icon="Search" class="mt-8 mb-12" placeholder="请输入关键词" clearable @change="search">
            <template v-if="loading" #suffix>
              <el-icon class="animate-spin animate-duration-1500"><Loading /></el-icon>
            </template>
          </el-input>
          <el-tooltip v-else :content="address" placement="bottom">
            <div class="h-32 leading-32 px-12 truncate rounded-base bg-white" b="~ border">{{ address }}</div>
          </el-tooltip>
          <el-scrollbar v-if="editable && (poiList.length > 1)" class="bg-white rounded-base" b="~ border" height="25.2vw">
            <div p="x-16 y-12">
              <template v-for="(item, index) in poiList" :key="item.lonlat">
                <div v-if="index !== 0" class="h-1 my-12 bg-border-lighter" />
                <div class="flex items-center leading-base gap-8 cursor-pointer" :class="{ 'text-primary': currentPos.lonlat === item.lonlat }" @click="select(item)">
                  <el-icon class="shrink-0" text="16"><LocationInformation /></el-icon>
                  <div class="overflow-hidden">
                    <header class="truncate">{{ item.name }}</header>
                    <el-tooltip :content="item.fullAddress">
                      <div class="truncate" text="small text-secondary">{{ item.fullAddress }}</div>
                    </el-tooltip>
                  </div>
                </div>
              </template>
            </div>
          </el-scrollbar>
        </aside>
      </div>
    </div>
    <template #footer>
      <div>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button v-if="editable" type="primary" @click="submit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
