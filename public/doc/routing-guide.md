# 🧭 路由使用指南

本文档介绍系统路由的配置和使用方法，包括单路由、一般二级路由、多级路由和详情页面路由的实现方式。

## 📑 路由结构说明

### 1. 基础路由配置

路由配置文件位于 `src/router/index.ts`，主要包含两部分：

- **📌 constantRoutes**: 基础路由（登录、404、重定向等）
- **🔄 asyncRoutes**: 动态路由（基于权限加载）

### 2. 路由元信息 (meta)

路由配置中的 `meta` 属性用于定义路由的元信息：

```typescript
meta: {
  title: string;        // 路由标题，显示在菜单和面包屑中
  icon?: string;        // 菜单图标
  hidden?: boolean;     // 是否在菜单中隐藏
  singleChild?: boolean;// 是否作为单菜单显示（只有一个子路由时）
  activeMenu?: string;  // 高亮的菜单路径（用于详情页等隐藏菜单）
  permission?: string;  // 权限标识
}
```

## 🌐 路由类型详解

### 1. 一般二级路由

最常见的路由配置方式，包含一个父菜单和多个子菜单：

```typescript
{
  path: '/form',
  component: Layout,
  meta: { title: '表单页面', icon: 'form' },
  children: [
    {
      path: 'base',
      component: () => import('@/views/template/Form.vue'),
      meta: { title: '基础表单' },
    },
    {
      path: 'supper',
      component: () => import('@/views/template/SupperForm.vue'),
      meta: { title: '高级表单' },
    },
  ],
}
```

**✨ 特点：**
- 父路由作为菜单分组，不可点击
- 子路由作为可点击的菜单项
- 面包屑显示完整层级：父菜单 / 子菜单


### 2. 单路由

单路由适用于只有一个页面的菜单项，配置时需要设置 `meta.singleChild: true`：

```typescript
{
  path: '/home',
  component: Layout,
  meta: { title: '单菜单', icon: 'list', singleChild: true },
  children: [
    {
      path: 'index',
      component: () => import('@/views/Empty.vue'),
    },
  ],
}
```

**✨ 特点：**
- 菜单中显示为单一菜单项
- 点击菜单直接跳转到子路由页面
- 面包屑只显示一级

### 3. 多级路由（三级及以上）

对于需要三级或更多级别的路由，使用 `MultilevelLayout` 组件作为中间层：

```typescript
{
  path: '/system',
  component: Layout,
  meta: { title: '系统管理', icon: 'file' },
  children: [
    {
      path: 'third',
      meta: { title: '三级目录测试', icon: 'home' },
      component: MultilevelLayout,    // 使用多级布局组件
      children: [
        {
          path: 'user',
          meta: { title: '用户管理', icon: 'home' },
          component: () => import('@/views/system/user/User.vue'),
        },
      ],
    },
  ],
}
```

**✨ 特点：**
- 中间层使用 `MultilevelLayout` 组件
- 支持无限级嵌套


### 4. 详情页面路由

详情页面通常从列表页跳转，不在菜单中显示，但需要保持父菜单高亮：

```typescript
{
  path: '/list',
  component: Layout,
  meta: { title: '列表页', icon: 'list' },
  children: [
    {
      path: 'monitor',
      component: () => import('@/views/template/Monitor.vue'),
      meta: { title: '监控' },
    },
    {
      path: 'monitor/:id',
      component: () => import('@/views/template/monitorDetail/MonitorDetail.vue'),
      meta: { 
        title: '监控详情', 
        hidden: true,                    // 不在菜单中显示
        activeMenu: '/list/monitor'      // 指定高亮的菜单
      },
    },
  ],
}
```

**✨ 特点：**
- 设置 `hidden: true` 隐藏菜单项
- 通过 `activeMenu` 指定需要高亮的父菜单

## 🔐 权限控制

通过 `meta.permission` 控制路由访问权限：

```typescript
{
  path: 'menu',
  component: () => import('@/views/system/menu/Menu.vue'),
  meta: {
    title: '菜单管理',
    icon: 'home',
    permission: '60000',
  },
}
```
